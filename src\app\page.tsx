import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { redirect } from "next/navigation";


const queryClient = new QueryClient();


export default function Home() {
  // Redirect to /login

  <QueryClientProvider client={queryClient}>

    <ReactQueryDevtools initialIsOpen={false} />
    redirect("/login");
  </QueryClientProvider >
}