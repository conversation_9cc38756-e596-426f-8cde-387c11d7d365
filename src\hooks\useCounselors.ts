import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { ApplicationStatus } from "@/types/application";

export interface CounselorStats {
  id: string;
  email: string;
  totalApplications: number;
  startedApplications: number;
  processingApplications: number;
  documentsSubmittedApplications: number;
  paymentsProcessedApplications: number;
  completedApplications: number;
}

export interface CounselorOption {
  id: string;
  role: string;
  email: string;
}

export interface CreateCounselorData {
  username: string;
  email: string;
  password: string;
}

// Fetch all counselors with their statistics
const fetchCounselorsWithStats = async (): Promise<CounselorStats[]> => {
  // Get all counselors from profiles
  const { data: profiles, error: profilesError } = await supabase
    .from("profiles")
    .select("id, role")
    .eq("role", "counselor");

  if (profilesError) throw profilesError;

  // Get user details and application stats for each counselor
  const counselorStats = await Promise.all(
    profiles.map(async (profile) => {
      // Get user email from auth
      const { data: userData } = await supabase.auth.admin.getUserById(profile.id);
      const email = userData?.user?.email || "Unknown";

      // Get application statistics
      const { data: applications, error: appsError } = await supabase
        .from("applications")
        .select("application_status")
        .eq("counselor_id", profile.id);

      if (appsError) {
        console.error("Error fetching applications for counselor:", appsError);
        return {
          id: profile.id,
          email,
          totalApplications: 0,
          startedApplications: 0,
          processingApplications: 0,
          documentsSubmittedApplications: 0,
          paymentsProcessedApplications: 0,
          completedApplications: 0,
        };
      }

      const stats = {
        id: profile.id,
        email,
        totalApplications: applications.length,
        startedApplications: applications.filter(app => app.application_status === ApplicationStatus.STARTED).length,
        processingApplications: applications.filter(app => app.application_status === ApplicationStatus.PROCESSING).length,
        documentsSubmittedApplications: applications.filter(app => app.application_status === ApplicationStatus.DOCUMENTS_SUBMITTED).length,
        paymentsProcessedApplications: applications.filter(app => app.application_status === ApplicationStatus.PAYMENTS_PROCESSED).length,
        completedApplications: applications.filter(app => app.application_status === ApplicationStatus.COMPLETED).length,
      };

      return stats;
    })
  );

  return counselorStats;
};

// Fetch counselors for dropdown options
const fetchCounselorOptions = async (): Promise<CounselorOption[]> => {
  // Fetch counselors with email information
  const { data: counselorsData, error: counselorsError } = await supabase
    .from("profiles")
    .select("id, role")
    .eq("role", "counselor");

  if (counselorsError) throw counselorsError;

  if (!counselorsData) return [];

  // Get email information for each counselor from auth.users
  const counselorsWithEmails = await Promise.all(
    counselorsData.map(async (counselor) => {
      try {
        const { data: userData } = await supabase.auth.admin.getUserById(counselor.id);
        return {
          id: counselor.id,
          role: counselor.role || "counselor",
          email: userData?.user?.email || "Unknown",
        };
      } catch (error) {
        console.error("Error fetching user email:", error);
        return {
          id: counselor.id,
          role: counselor.role || "counselor",
          email: "Unknown",
        };
      }
    })
  );

  return counselorsWithEmails;
};

// Create a new counselor
const createCounselor = async (data: CreateCounselorData) => {
  // Create user in Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: data.email,
    password: data.password,
    options: {
      data: {
        name: data.username,
        role: "counselor",
      },
    },
  });

  if (authError) throw authError;

  if (authData.user) {
    // Create or update the profile
    const { error: profileError } = await supabase
      .from("profiles")
      .upsert({
        id: authData.user.id,
        role: "counselor",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (profileError) throw profileError;
  }

  return authData;
};

// Hook to fetch counselors with statistics
export const useCounselorsWithStats = () => {
  return useQuery({
    queryKey: ["counselors", "stats"],
    queryFn: fetchCounselorsWithStats,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 1,
  });
};

// Hook to fetch counselor options for dropdowns
export const useCounselorOptions = () => {
  return useQuery({
    queryKey: ["counselors", "options"],
    queryFn: fetchCounselorOptions,
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    retry: 1,
  });
};

// Hook to create a new counselor
export const useCreateCounselor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCounselor,
    onSuccess: () => {
      // Invalidate counselor queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["counselors"] });
      toast.success("Counselor created successfully");
    },
    onError: (error: any) => {
      console.error("Error creating counselor:", error);
      toast.error("Failed to create counselor: " + (error.message || "Unknown error"));
    },
  });
};
