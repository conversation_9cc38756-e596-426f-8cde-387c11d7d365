{"name": "admin-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -H 0.0.0.0 -p 3001", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.2.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.5", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.20.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.474.0", "next": "15.1.6", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}